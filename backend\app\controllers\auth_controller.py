from flask import Blueprint, request, jsonify, make_response, current_app
from app.services.auth_service import AuthService
from app.utils.csrf_exempt import csrf_exempt
from app.utils.rate_limit import rate_limit
# Schema imports removed to avoid Marshmallow version conflicts
import logging
import os
from marshmallow import ValidationError
from functools import wraps
from firebase_admin import auth

auth_bp = Blueprint("auth", __name__)

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

auth_service = AuthService()

# Default CORS settings
CORS_ORIGIN = 'https://localhost:5173'
CORS_HEADERS = 'Content-Type,Authorization,X-CSRFToken'
CORS_METHODS = 'GET,PUT,POST,DELETE,OPTIONS'

def handle_options_request():
    """Handle OPTIONS request for CORS preflight."""
    response = jsonify({"message": "OK"})
    response.headers.add('Access-Control-Allow-Origin', request.headers.get('Origin', CORS_ORIGIN))
    response.headers.add('Access-Control-Allow-Headers', CORS_HEADERS)
    response.headers.add('Access-Control-Allow-Methods', CORS_METHODS)
    response.headers.add('Access-Control-Allow-Credentials', 'true')
    return response, 200

def add_cors_headers(response):
    """Add CORS headers to a response."""
    response.headers.add('Access-Control-Allow-Origin', request.headers.get('Origin', CORS_ORIGIN))
    response.headers.add('Access-Control-Allow-Headers', CORS_HEADERS)
    response.headers.add('Access-Control-Allow-Credentials', 'true')
    return response

def cors_enabled(f):
    """Decorator to enable CORS for a route."""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if request.method == "OPTIONS":
            return handle_options_request()
        response = f(*args, **kwargs)
        if isinstance(response, tuple):
            response_obj, status_code = response
            if isinstance(response_obj, (dict, list)):
                response_obj = jsonify(response_obj)
            response_obj = add_cors_headers(response_obj)
            return response_obj, status_code
        else:
            return add_cors_headers(response)
    return decorated_function

@auth_bp.route("/verify", methods=["POST", "OPTIONS"])
@csrf_exempt
@rate_limit("60/minute")
@cors_enabled
def verify():
    """
    Verify a Firebase ID token and return user data.

    This endpoint verifies the provided Firebase ID token and returns the user data
    if the token is valid. It also sets a session timeout cookie.

    Request body:
        token (str): The Firebase ID token to verify

    Returns:
        User data if the token is valid, error message otherwise
    """
    data = request.get_json()
    if not data:
        logger.warning("Token verification failed: No data provided")
        return {"error": "No data provided"}, 400

    try:
        # Simple validation without schema to avoid Marshmallow version conflicts
        token = data.get("token")
        if not token:
            logger.warning("Token verification failed: Token is required")
            return {"error": "Token is required"}, 400

        if len(token) < 10:
            logger.warning("Token verification failed: Invalid token format")
            return {"error": "Invalid token format"}, 400
        ip_address = request.remote_addr
        user_agent = request.headers.get("User-Agent")

        user_data = auth_service.verify_token(token, ip_address, user_agent)
        logger.info(f"Token verified for user: {user_data['email']}")

        # Create response with user data
        response = make_response(jsonify(user_data), 200)

        # Flask-SeaSurf automatically adds the CSRF token to the response
        # when CSRF is enabled

        # Store the JWT token in an HttpOnly cookie (1 hour)
        # For deployed environment, we need to be more careful with cookie settings
        is_production = os.getenv("FLASK_ENV") == "production"

        # In production with cross-origin requests, HttpOnly cookies can be problematic
        # Let's also store the token in a way the frontend can access it
        if is_production:
            # Don't use HttpOnly in production to allow frontend access
            response.set_cookie(
                'auth_token',
                value=token,
                max_age=3600,  # 1 hour in seconds
                secure=True,  # Require HTTPS in production
                httponly=False,  # Allow frontend access in production
                samesite='None',  # Allow cross-site in production
                domain=None  # Let browser determine domain
            )
        else:
            # Use HttpOnly in development for better security
            response.set_cookie(
                'auth_token',
                value=token,
                max_age=3600,  # 1 hour in seconds
                secure=False,  # Allow HTTP in development
                httponly=True,
                samesite='Lax',
                domain=None
            )

        # Set session timeout (1 hour)
        response.set_cookie(
            'session_timeout',
            value='true',
            max_age=3600,  # 1 hour in seconds
            secure=True,
            httponly=True,
            samesite='Strict'
        )

        # Add CORS headers (will be added by the decorator)
        return response
    except ValidationError as e:
        logger.warning(f"Token verification validation error: {e.messages}")
        return {"error": "Validation error", "details": e.messages}, 400
    except auth.ExpiredIdTokenError:
        logger.warning("Token has expired")
        return {"error": "Token has expired", "code": "token_expired"}, 401
    except auth.InvalidIdTokenError:
        logger.warning("Invalid token")
        return {"error": "Invalid token", "code": "token_invalid"}, 401
    except Exception as e:
        logger.error(f"Token verification failed: {str(e)}")
        return {"error": str(e)}, 401

@auth_bp.route("/logout", methods=["POST", "OPTIONS"])
@csrf_exempt
@rate_limit("60/minute")
@cors_enabled
def logout():
    """
    Logout a user by clearing their session.

    This endpoint handles user logout by clearing session cookies.
    It accepts an optional Authorization header with a Bearer token,
    or a token in the request body.

    Request body (optional):
        token (str): The Firebase ID token to invalidate

    Returns:
        A success message and clears session cookies
    """
    # First check for token in request body
    data = request.get_json() or {}

    # Simple validation without schema to avoid Marshmallow version conflicts
    if data and data.get("token") and len(data.get("token", "")) < 10:
        logger.warning("Logout validation failed: Invalid token format")
        # Continue with logout despite validation errors

    # Get token from Authorization header if not in body
    token = data.get("token")
    if not token:
        auth_header = request.headers.get("Authorization")
        if auth_header and auth_header.startswith("Bearer "):
            token = auth_header.split(" ")[1]

    if not token:
        logger.warning("No token provided in logout request")
        # Still return success for better UX, but make sure to clear cookies
        response = make_response(jsonify({"message": "Logged out successfully"}), 200)

        # Clear all cookies even without a token
        response.set_cookie('session_timeout', '', expires=0, secure=True, httponly=True, samesite='Strict', path='/')
        response.set_cookie('auth_token', '', expires=0, secure=True, httponly=True, samesite='Strict', path='/')
        response.set_cookie('_csrf_token', '', expires=0, secure=True, samesite='Strict', path='/')
        response.set_cookie('X-CSRFToken', '', expires=0, secure=True, samesite='Strict', path='/')

        return response

    try:
        # Attempt to get user info from token for logging purposes
        try:
            decoded_token = auth.verify_id_token(token)
            user_id = decoded_token.get("uid", "unknown")
            logger.info(f"User {user_id} logged out")

            # Here you could implement token blacklisting if needed
            # For example, by storing the token in a Redis cache with the token's expiry time
            # This would require additional infrastructure
        except Exception as token_error:
            # If token verification fails, still proceed with logout
            logger.info(f"User with invalid token logged out: {str(token_error)}")

        # Create response
        response = make_response(jsonify({"message": "Logged out successfully"}), 200)

        # Clear session cookies with all necessary attributes
        response.set_cookie('session_timeout', '', expires=0, secure=True, httponly=True, samesite='Strict', path='/')
        response.set_cookie('auth_token', '', expires=0, secure=True, httponly=True, samesite='Strict', path='/')

        # Clear CSRF cookie - Flask-SeaSurf uses '_csrf_token' by default
        response.set_cookie('_csrf_token', '', expires=0, secure=True, samesite='Strict', path='/')

        # Clear any other cookies that might be set
        response.set_cookie('X-CSRFToken', '', expires=0, secure=True, samesite='Strict', path='/')

        return response
    except Exception as e:
        logger.error(f"Error during logout: {str(e)}")
        # Still return success for better UX, but make sure to clear cookies
        response = make_response(jsonify({"message": "Logged out successfully"}), 200)

        # Clear all cookies even in case of error
        response.set_cookie('session_timeout', '', expires=0, secure=True, httponly=True, samesite='Strict', path='/')
        response.set_cookie('auth_token', '', expires=0, secure=True, httponly=True, samesite='Strict', path='/')
        response.set_cookie('_csrf_token', '', expires=0, secure=True, samesite='Strict', path='/')
        response.set_cookie('X-CSRFToken', '', expires=0, secure=True, samesite='Strict', path='/')

        return response

@auth_bp.route("/csrf-token", methods=["GET", "OPTIONS"])
@rate_limit("60/minute")
@cors_enabled
def get_csrf_token():
    """
    Endpoint to get a new CSRF token.

    This endpoint returns a CSRF token if CSRF protection is enabled,
    otherwise it returns a message indicating that CSRF is disabled.

    Returns:
        A response with a CSRF token or a message indicating CSRF is disabled
    """
    # Check if CSRF is enabled
    if current_app.config.get('CSRF_DISABLE', False):
        logger.info("CSRF token request received (CSRF is disabled)")
        return {"message": "CSRF is disabled"}, 200

    # Generate a new CSRF token
    logger.info("CSRF token request received")
    response = make_response(jsonify({"message": "CSRF token generated"}), 200)

    # Configure CSRF cookie with same settings as auth token for cross-origin compatibility
    is_production = os.getenv("FLASK_ENV") == "production"

    # Flask-SeaSurf automatically adds the CSRF token to the response, but we need to ensure
    # the cookie settings are compatible with cross-origin requests
    if is_production:
        # In production, we need to allow cross-origin access to CSRF tokens
        response.headers['Access-Control-Allow-Credentials'] = 'true'

    return response

@auth_bp.route("/current-user", methods=["GET", "OPTIONS"])
@rate_limit("60/minute")
@cors_enabled
def get_current_user():
    """
    Get the current user data using the HttpOnly auth_token cookie.

    This endpoint verifies the auth_token cookie and returns the user data
    if the token is valid.

    Returns:
        User data if the token is valid, error message otherwise
    """
    # Get token from cookie
    token = request.cookies.get('auth_token')
    if not token:
        logger.warning("No auth_token cookie found")
        return {"error": "Not authenticated"}, 401

    try:
        # Verify the token
        decoded_token = auth.verify_id_token(token)

        # Get user from database
        from app.models.user import User
        user = User.query.filter_by(firebase_uid=decoded_token["uid"]).first()

        if not user:
            logger.warning(f"User not found for firebase_uid: {decoded_token['uid']}")
            return {"error": "User not found"}, 404

        # Return user data
        user_data = {
            "id": user.id,
            "email": user.email,
            "name": user.name,
            "role": user.role,
            "firebase_uid": user.firebase_uid
        }

        logger.info(f"Current user retrieved: {user.email}")
        return jsonify(user_data), 200

    except auth.ExpiredIdTokenError:
        logger.error("Token verification failed: Token has expired")
        return {"error": "Token has expired"}, 401
    except auth.InvalidIdTokenError:
        logger.error("Token verification failed: Invalid token")
        return {"error": "Invalid token"}, 401
    except Exception as e:
        logger.error(f"Token verification failed: {str(e)}")
        return {"error": f"Token verification failed: {str(e)}"}, 401