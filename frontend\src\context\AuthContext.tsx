import React, { createContext, useContext, useState, useEffect, useCallback } from "react";
import { auth } from "../firebase";
import { signInWithEmailAndPassword, signOut, onAuthStateChanged } from "firebase/auth";
import * as authService from "../services/authService";
// Socket service removed
import { User } from "../types/user";
import api from "../api";

interface AuthContextType {
  user: User | null;
  setUser: React.Dispatch<React.SetStateAction<User | null>>;
  login: (email: string, password: string) => Promise<User>;
  logout: () => Promise<void>;
  loading: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [showSessionWarning, setShowSessionWarning] = useState(false);

  // Session timeout handlers
  const handleSessionExpired = useCallback(async () => {
    console.log('Session expired - logging out user');
    setShowSessionWarning(false);
    await logout();
    alert('Uw sessie is verlopen. U wordt automatisch uitgelogd.');
  }, []);

  const handleSessionWarning = useCallback(() => {
    console.log('Session warning - 5 minutes remaining');
    setShowSessionWarning(true);
  }, []);

  const extendSession = useCallback(() => {
    console.log('Session extended by user');
    setShowSessionWarning(false);
    authService.resetSessionTimeout(handleSessionExpired, handleSessionWarning);
  }, [handleSessionExpired, handleSessionWarning]);

  // Auto-hide session warning after 5 minutes if user doesn't respond
  useEffect(() => {
    if (showSessionWarning) {
      const timeoutId = setTimeout(() => {
        setShowSessionWarning(false);
      }, 5 * 60 * 1000); // 5 minutes

      return () => clearTimeout(timeoutId);
    }
  }, [showSessionWarning]);

  // Activity tracker - reset session timeout on user activity
  useEffect(() => {
    if (!user) return;

    let activityTimer: NodeJS.Timeout;

    const resetActivityTimer = () => {
      clearTimeout(activityTimer);
      // Reset session timeout after 30 seconds of inactivity
      activityTimer = setTimeout(() => {
        if (user && !showSessionWarning) {
          authService.resetSessionTimeout(handleSessionExpired, handleSessionWarning);
        }
      }, 30000); // 30 seconds
    };

    const handleActivity = () => {
      resetActivityTimer();
    };

    // Listen for user activity
    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];
    events.forEach(event => {
      document.addEventListener(event, handleActivity, true);
    });

    // Start the timer
    resetActivityTimer();

    return () => {
      clearTimeout(activityTimer);
      events.forEach(event => {
        document.removeEventListener(event, handleActivity, true);
      });
    };
  }, [user, showSessionWarning, handleSessionExpired, handleSessionWarning]);

  // Fetch CSRF token when the app starts
  useEffect(() => {
    const fetchCSRFToken = async () => {
      try {
        await authService.fetchCSRFToken();
        console.log('CSRF token fetched successfully');
      } catch (error) {
        console.error('Failed to fetch CSRF token:', error);
      }
    };

    fetchCSRFToken();
  }, []);

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (firebaseUser) => {
      if (firebaseUser) {
        try {
          const token = await firebaseUser.getIdToken();
          const userData = await authService.verifyToken(token);
          setUser(userData);

          // Start session timeout
          authService.startSessionTimeout(handleSessionExpired, handleSessionWarning);

          // User data is now stored in an HttpOnly cookie on the server
          // We don't need to store it in localStorage anymore

          // WebSocket connection removed
        } catch (error: any) {
          console.error("Failed to verify token:", error);
          // Check if this is a canceled request (timeout) - don't log out the user in this case
          if (error?.name === 'CanceledError' || error?.code === 'ERR_CANCELED') {
            console.log('Token verification request was canceled (timeout). User session maintained.');
            // Try to fetch user data from the server using the HttpOnly cookie
            try {
              const response = await api.get('/auth/current-user');
              if (response.data) {
                setUser(response.data);
                // Start session timeout for existing session
                authService.startSessionTimeout(handleSessionExpired, handleSessionWarning);
                return;
              }
            } catch (e) {
              console.error('Failed to get current user data:', e);
            }
          } else {
            // For other errors, log out the user
            setUser(null);
          }
        }
      } else {
        setUser(null);
      }
      setLoading(false);
    });
    return () => unsubscribe();
  }, []);

  const login = async (email: string, password: string): Promise<User> => {
    try {
      const userCredential = await signInWithEmailAndPassword(auth, email, password);
      const token = await userCredential.user.getIdToken();

      try {
        const userData = await authService.verifyToken(token);
        setUser(userData);

        // Start session timeout
        authService.startSessionTimeout(handleSessionExpired, handleSessionWarning);

        // User data is now stored in an HttpOnly cookie on the server
        // We don't need to store it in localStorage anymore

        // WebSocket connection removed
        return userData;
      } catch (error: any) {
        // Handle token verification errors
        console.error("Failed to verify token during login:", error);

        if (error?.name === 'CanceledError' || error?.code === 'ERR_CANCELED') {
          // If it's a timeout/canceled error, try to use Firebase user data as fallback
          console.log('Token verification request was canceled (timeout). Using Firebase data as fallback.');

          const fallbackUserData: User = {
            id: 0, // We don't know the actual ID
            email: userCredential.user.email || '',
            role: 'unknown', // We don't know the role
            name: userCredential.user.displayName || '',
            firebase_uid: userCredential.user.uid
          };

          setUser(fallbackUserData);
          return fallbackUserData;
        } else {
          // For other errors, propagate the error
          throw error;
        }
      }
    } catch (error) {
      // Handle Firebase authentication errors
      console.error("Failed to login with Firebase:", error);
      throw error;
    }
  };

  const logout = async () => {
    try {
      // Clear session timeout
      authService.clearSessionTimeout();
      setShowSessionWarning(false);

      // Call the backend logout endpoint to invalidate the session
      await authService.logout();
      // WebSocket disconnection removed
      // Then sign out from Firebase
      await signOut(auth);
      setUser(null);
      // No need to remove from localStorage as we're using HttpOnly cookies now
    } catch (error: any) {
      console.error('Error during logout:', error);

      // If it's a timeout/canceled error, still proceed with logout
      if (error?.name === 'CanceledError' || error?.code === 'ERR_CANCELED') {
        console.log('Logout request was canceled (timeout). Proceeding with client-side logout.');
      }

      // Clear session timeout even on error
      authService.clearSessionTimeout();
      setShowSessionWarning(false);

      // WebSocket disconnection removed
      // Still try to sign out from Firebase even if backend logout fails
      await signOut(auth);
      setUser(null);
      // No need to remove from localStorage as we're using HttpOnly cookies now
    }
  };

  return (
    <AuthContext.Provider value={{ user, setUser, login, logout, loading }}>
      {children}

      {/* Session Warning Modal */}
      {showSessionWarning && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Sessie verloopt binnenkort
            </h3>
            <p className="text-gray-600 mb-6">
              Uw sessie verloopt over 5 minuten. Wilt u ingelogd blijven?
            </p>
            <div className="flex space-x-3">
              <button
                onClick={extendSession}
                className="flex-1 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
              >
                Ja, blijf ingelogd
              </button>
              <button
                onClick={logout}
                className="flex-1 bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors"
              >
                Uitloggen
              </button>
            </div>
          </div>
        </div>
      )}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};
