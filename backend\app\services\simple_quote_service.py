"""
Simple Quote service module.
This module provides business logic for simple quotes.
"""
from typing import List, Dict, Optional, Tuple, Any
import logging
from datetime import datetime, timedelta, timezone
from app.models.simple_quote import SimpleQuote, QuoteCategory, PaymentTerms, QuoteStatus
from app.repositories.customer_repository import CustomerRepository
from app.repositories.document_repository import DocumentRepository
from app.utils.simple_quote_products import (
    PRICING_CONFIG, DISCOUNT_LIMITS, get_product_by_key, calculate_monthly_increase
)
from app import db

# Configure logging
logger = logging.getLogger(__name__)

class SimpleQuoteService:
    """Service for simple quote operations."""

    def __init__(self):
        """Initialize the service with repositories."""
        self.customer_repo = CustomerRepository()
        self.document_repo = DocumentRepository()

    def create_quote(self, quote_data: Dict, created_by: int) -> Dict:
        """
        Create a new simple quote.

        Args:
            quote_data: Quote data
            created_by: User ID who created the quote

        Returns:
            Created quote data
        """
        try:
            # Map frontend values to enum values
            # Map frontend values to database enum values (UPPERCASE)
            category_mapping = {
                'alarm': 'ALARM',
                'cameras': 'CAMERAS',
                'alarm_cameras': 'ALARM_CAMERAS'
            }

            payment_mapping = {
                '1_term': 'ONE_TERM',
                '2_terms': 'TWO_TERMS',
                '3_terms': 'THREE_TERMS'
            }

            # Get mapped values
            category_value = category_mapping.get(quote_data['category'], quote_data['category'])
            payment_value = payment_mapping.get(quote_data.get('payment_terms', '1_term'), '1_term')

            # Create quote instance - gebruik enum waarden direct
            quote = SimpleQuote(
                created_by=created_by,
                category=category_value,  # Direct de string waarde gebruiken
                payment_terms=payment_value,  # Direct de string waarde gebruiken
                customer_name=quote_data['customer_name'],
                customer_email=quote_data.get('customer_email'),
                customer_phone=quote_data.get('customer_phone'),
                customer_address=quote_data.get('customer_address'),
                customer_city=quote_data.get('customer_city'),
                customer_postal_code=quote_data.get('customer_postal_code'),
                discount_percentage=float(quote_data.get('discount_percentage', 0))
            )

            # Set product quantities
            quote.extra_magneetcontact = quote_data.get('extra_magneetcontact', 0)
            quote.extra_shock_sensor = quote_data.get('extra_shock_sensor', 0)
            quote.extra_pir_normaal = quote_data.get('extra_pir_normaal', 0)
            quote.extra_rookmelder = quote_data.get('extra_rookmelder', 0)
            quote.extra_pircam = quote_data.get('extra_pircam', 0)
            quote.extra_bediendeel = quote_data.get('extra_bediendeel', 0)
            quote.extra_sirene = quote_data.get('extra_sirene', 0)
            quote.videodoorbell_free = quote_data.get('videodoorbell_free', False)
            quote.videodoorbell_paid = quote_data.get('videodoorbell_paid', False)

            # Calculate totals
            quote.calculate_totals()

            # Generate quote number
            quote.quote_number = self._generate_quote_number()

            # Save to database
            db.session.add(quote)
            db.session.commit()

            logger.info(f"Created simple quote {quote.quote_number} for {quote.customer_name}")
            return quote.to_dict()

        except Exception as e:
            db.session.rollback()
            logger.error(f"Failed to create simple quote: {str(e)}")
            logger.error(f"Quote data: {quote_data}")

            # More specific error messages
            if "simple_quotes_category_check" in str(e):
                raise Exception(f"Invalid category value: {quote_data.get('category')}. Must be one of: alarm, cameras, alarm_cameras")
            elif "simple_quotes_payment_terms_check" in str(e):
                raise Exception(f"Invalid payment terms: {quote_data.get('payment_terms')}. Must be one of: 1_term, 2_terms, 3_terms")
            else:
                raise Exception(f"Failed to create quote: {str(e)}")

    def get_quote_by_id(self, quote_id: int) -> Optional[Dict]:
        """
        Get a quote by ID.

        Args:
            quote_id: Quote ID

        Returns:
            Quote data or None if not found
        """
        try:
            quote = SimpleQuote.query.get(quote_id)
            if not quote:
                return None

            return quote.to_dict()

        except Exception as e:
            logger.error(f"Failed to fetch quote {quote_id}: {str(e)}")
            raise Exception(f"Failed to fetch quote: {str(e)}")

    def update_quote(self, quote_id: int, quote_data: Dict) -> Dict:
        """
        Update a quote.

        Args:
            quote_id: Quote ID
            quote_data: Updated quote data

        Returns:
            Updated quote data
        """
        try:
            quote = SimpleQuote.query.get(quote_id)
            if not quote:
                raise Exception("Quote not found")

            # Only allow updates if quote is still draft
            if quote.status != QuoteStatus.DRAFT:
                raise Exception("Cannot update quote that is no longer in draft status")

            # Update fields
            if 'customer_name' in quote_data:
                quote.customer_name = quote_data['customer_name']
            if 'customer_email' in quote_data:
                quote.customer_email = quote_data['customer_email']
            if 'customer_phone' in quote_data:
                quote.customer_phone = quote_data['customer_phone']
            if 'customer_address' in quote_data:
                quote.customer_address = quote_data['customer_address']
            if 'customer_city' in quote_data:
                quote.customer_city = quote_data['customer_city']
            if 'customer_postal_code' in quote_data:
                quote.customer_postal_code = quote_data['customer_postal_code']
            if 'payment_terms' in quote_data:
                quote.payment_terms = PaymentTerms(quote_data['payment_terms'])
            if 'discount_percentage' in quote_data:
                quote.discount_percentage = float(quote_data['discount_percentage'])

            # Update product quantities
            if 'extra_magneetcontact' in quote_data:
                quote.extra_magneetcontact = quote_data['extra_magneetcontact']
            if 'extra_shock_sensor' in quote_data:
                quote.extra_shock_sensor = quote_data['extra_shock_sensor']
            if 'extra_pir_normaal' in quote_data:
                quote.extra_pir_normaal = quote_data['extra_pir_normaal']
            if 'extra_rookmelder' in quote_data:
                quote.extra_rookmelder = quote_data['extra_rookmelder']
            if 'extra_pircam' in quote_data:
                quote.extra_pircam = quote_data['extra_pircam']
            if 'extra_bediendeel' in quote_data:
                quote.extra_bediendeel = quote_data['extra_bediendeel']
            if 'extra_sirene' in quote_data:
                quote.extra_sirene = quote_data['extra_sirene']
            if 'videodoorbell_free' in quote_data:
                quote.videodoorbell_free = quote_data['videodoorbell_free']
            if 'videodoorbell_paid' in quote_data:
                quote.videodoorbell_paid = quote_data['videodoorbell_paid']

            # Recalculate totals
            quote.calculate_totals()

            # Update timestamp
            quote.updated_at = datetime.utcnow()

            db.session.commit()

            logger.info(f"Updated simple quote {quote.quote_number}")
            return quote.to_dict()

        except Exception as e:
            db.session.rollback()
            logger.error(f"Failed to update quote {quote_id}: {str(e)}")
            raise Exception(f"Failed to update quote: {str(e)}")

    def sign_quote(self, quote_id: int, signature_data: str) -> Dict:
        """
        Sign a quote and create customer if needed.

        Args:
            quote_id: Quote ID
            signature_data: Base64 signature data

        Returns:
            Updated quote data with customer information
        """
        try:
            quote = SimpleQuote.query.get(quote_id)
            if not quote:
                raise Exception("Quote not found")

            if quote.status == QuoteStatus.SIGNED:
                raise Exception("Quote is already signed")

            # Create customer if not exists
            if not quote.customer_id:
                customer_data = {
                    'name': quote.customer_name,
                    'email': quote.customer_email,
                    'phone': quote.customer_phone,
                    'address': quote.customer_address,
                    'city': quote.customer_city,
                    'postal_code': quote.customer_postal_code
                }
                customer = self.customer_repo.create(customer_data)
                quote.customer_id = customer.id

            # Update quote status
            quote.status = QuoteStatus.SIGNED
            quote.signature_data = signature_data
            quote.signed_at = datetime.utcnow()

            db.session.commit()

            logger.info(f"Signed simple quote {quote.quote_number}")
            return quote.to_dict()

        except Exception as e:
            db.session.rollback()
            logger.error(f"Failed to sign quote {quote_id}: {str(e)}")
            raise Exception(f"Failed to sign quote: {str(e)}")

    def get_all_quotes(self, page: int = 1, per_page: int = 20) -> Tuple[List[Dict], int]:
        """
        Get all quotes with pagination.

        Args:
            page: Page number
            per_page: Number of items per page

        Returns:
            Tuple of quotes list and total count
        """
        try:
            quotes_query = SimpleQuote.query.order_by(SimpleQuote.created_at.desc())
            total = quotes_query.count()
            
            quotes = quotes_query.offset((page - 1) * per_page).limit(per_page).all()
            
            return [quote.to_dict() for quote in quotes], total

        except Exception as e:
            logger.error(f"Failed to fetch quotes: {str(e)}")
            raise Exception(f"Failed to fetch quotes: {str(e)}")

    def _generate_quote_number(self) -> str:
        """
        Generate a unique quote number.

        Returns:
            Quote number string
        """
        from datetime import datetime

        # Get the current year
        current_year = datetime.now().year

        # Find the highest quote number for the current year
        latest_quote = SimpleQuote.query.filter(
            SimpleQuote.quote_number.like(f"SQ{current_year}-%")
        ).order_by(SimpleQuote.quote_number.desc()).first()

        if latest_quote and latest_quote.quote_number:
            # Extract the number part and increment
            try:
                year_part, number = latest_quote.quote_number.split('-')
                next_number = int(number) + 1
            except (ValueError, IndexError):
                # If the format is not as expected, start from 1
                next_number = 1
        else:
            # No quotes for this year yet
            next_number = 1

        return f"SQ{current_year}-{next_number:04d}"
