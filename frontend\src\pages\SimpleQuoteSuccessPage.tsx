import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { FaCheckCircle, FaFileDownload, FaHome, FaPlus } from 'react-icons/fa';
import Breadcrumbs from '../components/Breadcrumbs';
import { MobileContainer, MobilePageHeader } from '../components/common/MobileUtils';
import simpleQuoteService from '../services/simpleQuoteService';

const SimpleQuoteSuccessPage: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  
  const { quoteId, quoteNumber } = location.state || {};

  // Redirect if no quote data
  if (!quoteId || !quoteNumber) {
    navigate('/simple-quotes');
    return null;
  }

  const handleDownloadPDF = async () => {
    try {
      const blob = await simpleQuoteService.downloadQuotePDF(quoteId);
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `offerte-${quoteNumber}.pdf`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('Error downloading PDF:', error);
      alert('Fout bij downloaden van PDF');
    }
  };

  return (
    <MobileContainer>
      <Breadcrumbs />
      
      <MobilePageHeader
        title="Offerte Succesvol Aangemaakt"
        subtitle="Uw offerte is ondertekend en opgeslagen"
      />

      <div className="space-y-6">
        {/* Success message */}
        <div className="bg-green-50 border border-green-200 rounded-lg p-6 text-center">
          <FaCheckCircle className="text-green-500 text-6xl mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-green-900 mb-2">
            Gefeliciteerd!
          </h2>
          <p className="text-green-800 mb-4">
            Uw offerte is succesvol aangemaakt en ondertekend.
          </p>
          <div className="bg-white rounded-lg p-4 inline-block">
            <span className="text-sm text-gray-600">Offertenummer:</span>
            <div className="text-xl font-bold text-gray-900">{quoteNumber}</div>
          </div>
        </div>

        {/* Next steps */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h3 className="font-semibold text-blue-900 mb-3">Wat gebeurt er nu?</h3>
          <ul className="space-y-2 text-sm text-blue-800">
            <li className="flex items-start">
              <span className="w-6 h-6 bg-blue-200 text-blue-800 rounded-full flex items-center justify-center text-xs font-bold mr-3 mt-0.5">1</span>
              <span>Uw offerte wordt verwerkt door ons team</span>
            </li>
            <li className="flex items-start">
              <span className="w-6 h-6 bg-blue-200 text-blue-800 rounded-full flex items-center justify-center text-xs font-bold mr-3 mt-0.5">2</span>
              <span>U ontvangt binnen 24 uur een bevestiging per email</span>
            </li>
            <li className="flex items-start">
              <span className="w-6 h-6 bg-blue-200 text-blue-800 rounded-full flex items-center justify-center text-xs font-bold mr-3 mt-0.5">3</span>
              <span>Onze technicus neemt contact op voor de installatie afspraak</span>
            </li>
            <li className="flex items-start">
              <span className="w-6 h-6 bg-blue-200 text-blue-800 rounded-full flex items-center justify-center text-xs font-bold mr-3 mt-0.5">4</span>
              <span>Professionele installatie op de afgesproken datum</span>
            </li>
          </ul>
        </div>

        {/* Contact information */}
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
          <h3 className="font-semibold text-gray-900 mb-3">Contact & Support</h3>
          <div className="space-y-2 text-sm text-gray-700">
            <div>
              <span className="font-medium">Telefoon:</span> 020-1234567
            </div>
            <div>
              <span className="font-medium">Email:</span> <EMAIL>
            </div>
            <div>
              <span className="font-medium">Openingstijden:</span> Ma-Vr 08:00-17:00
            </div>
          </div>
          <p className="text-xs text-gray-600 mt-3">
            Heeft u vragen over uw offerte? Neem gerust contact met ons op en vermeld uw offertenummer.
          </p>
        </div>

        {/* Action buttons */}
        <div className="space-y-3">
          <button
            onClick={handleDownloadPDF}
            className="w-full bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors font-medium flex items-center justify-center"
          >
            <FaFileDownload className="mr-2" />
            Download Offerte PDF
          </button>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            <button
              onClick={() => navigate('/simple-quotes')}
              className="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors font-medium flex items-center justify-center"
            >
              <FaPlus className="mr-2" />
              Nieuwe Offerte
            </button>

            <button
              onClick={() => navigate('/dashboard')}
              className="bg-gray-600 text-white px-6 py-3 rounded-lg hover:bg-gray-700 transition-colors font-medium flex items-center justify-center"
            >
              <FaHome className="mr-2" />
              Naar Dashboard
            </button>
          </div>
        </div>

        {/* Important notes */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <h3 className="font-semibold text-yellow-900 mb-2">Belangrijk:</h3>
          <ul className="text-sm text-yellow-800 space-y-1">
            <li>• Bewaar uw offertenummer voor eventuele vragen</li>
            <li>• De offerte is 30 dagen geldig</li>
            <li>• Wijzigingen zijn mogelijk tot de installatie datum</li>
            <li>• Bij annulering binnen 14 dagen geen kosten</li>
          </ul>
        </div>

        {/* Social proof / testimonial */}
        <div className="bg-white border border-gray-200 rounded-lg p-4 text-center">
          <div className="text-yellow-400 text-2xl mb-2">⭐⭐⭐⭐⭐</div>
          <p className="text-sm text-gray-600 italic mb-2">
            "Uitstekende service en professionele installatie. Zeer tevreden met ons nieuwe alarmsysteem!"
          </p>
          <p className="text-xs text-gray-500">- Familie van der Berg, Amsterdam</p>
        </div>
      </div>
    </MobileContainer>
  );
};

export default SimpleQuoteSuccessPage;
