"""
Simple Quote controller module.
This module handles API endpoints for the simple quote system.
"""
import logging
from flask import Blueprint, request, jsonify, send_from_directory, make_response
from app.utils.security import roles_required
from app.utils.rate_limit import rate_limit
from app.utils.simple_quote_products import (
    BASE_ALARM_PACKAGE, EXTRA_PRODUCTS, CAMERA_PRODUCTS,
    PRICING_CONFIG, DISCOUNT_LIMITS, get_products_for_category,
    get_product_by_key, calculate_monthly_increase
)
from app.services.simple_quote_service import SimpleQuoteService
from app.services.simple_quote_pdf_service import SimpleQuotePDFService
import os

# Configure logging
logger = logging.getLogger(__name__)

# Create blueprint
simple_quote_bp = Blueprint('simple_quote', __name__)

# Initialize services
simple_quote_service = SimpleQuoteService()
pdf_service = SimpleQuotePDFService()

@simple_quote_bp.route("/products", methods=["GET"])
@roles_required("administrator", "verkoper")
@rate_limit("100/minute")
def get_products():
    """
    Get all available products for simple quotes.
    
    Returns:
        JSON: Product catalog organized by category.
    """
    try:
        category = request.args.get("category", "all")
        
        if category == "all":
            products = {
                "base_alarm_package": BASE_ALARM_PACKAGE,
                "extra_products": EXTRA_PRODUCTS,
                "camera_products": CAMERA_PRODUCTS
            }
        else:
            # Get products for specific category and structure them properly
            category_products = get_products_for_category(category)

            # Separate base package from extra products
            base_package = {}
            extra_products = {}

            for key, product in category_products.items():
                if key in BASE_ALARM_PACKAGE:
                    base_package[key] = product
                else:
                    extra_products[key] = product

            products = {
                "base_package": base_package,
                "extra_products": extra_products
            }
        
        return jsonify({
            "products": products,
            "pricing_config": PRICING_CONFIG,
            "discount_limits": DISCOUNT_LIMITS
        }), 200
        
    except Exception as e:
        logger.error(f"Failed to fetch products: {str(e)}")
        return jsonify({"error": str(e)}), 500

@simple_quote_bp.route("/products/<product_key>", methods=["GET"])
@roles_required("administrator", "verkoper")
@rate_limit("100/minute")
def get_product(product_key):
    """
    Get specific product information.
    
    Args:
        product_key: Product identifier
        
    Returns:
        JSON: Product information.
    """
    try:
        product = get_product_by_key(product_key)
        
        if not product:
            return jsonify({"error": "Product not found"}), 404
            
        return jsonify({"product": product}), 200
        
    except Exception as e:
        logger.error(f"Failed to fetch product {product_key}: {str(e)}")
        return jsonify({"error": str(e)}), 500

@simple_quote_bp.route("/product-images/<path:filename>", methods=["GET"])
@rate_limit("200/minute")
def get_product_image(filename):
    """
    Serve product images including subdirectories.

    Args:
        filename: Image filename (can include subdirectory path)

    Returns:
        Image file or 404 if not found.
    """
    try:
        # Security: only allow specific image extensions
        allowed_extensions = {'.jpg', '.jpeg', '.png', '.gif'}
        file_ext = os.path.splitext(filename)[1].lower()

        if file_ext not in allowed_extensions:
            return jsonify({"error": "Invalid file type"}), 400

        # Security: prevent directory traversal
        if '..' in filename or filename.startswith('/'):
            return jsonify({"error": "Invalid file path"}), 400

        # Serve from static/product_images directory
        return send_from_directory(
            os.path.join('app', 'static', 'product_images'),
            filename
        )

    except FileNotFoundError:
        logger.warning(f"Product image not found: {filename}")
        return jsonify({"error": "Image not found"}), 404
    except Exception as e:
        logger.error(f"Failed to serve product image {filename}: {str(e)}")
        return jsonify({"error": str(e)}), 500

@simple_quote_bp.route("/calculate", methods=["POST"])
@roles_required("administrator", "verkoper")
@rate_limit("50/minute")
def calculate_quote():
    """
    Calculate quote totals based on selected products.
    
    Returns:
        JSON: Calculated totals and pricing breakdown.
    """
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({"error": "No data provided"}), 400
            
        # Extract product quantities with safe defaults
        extra_products = data.get('extra_products', {})
        payment_terms = data.get('payment_terms', '1_term')

        # Safe conversion of discount percentage
        try:
            discount_percentage = float(data.get('discount_percentage', 0))
        except (ValueError, TypeError):
            discount_percentage = 0.0

        videodoorbell_free = bool(data.get('videodoorbell_free', False))
        videodoorbell_paid = bool(data.get('videodoorbell_paid', False))
        
        # Validate discount percentage
        max_discount = DISCOUNT_LIMITS.get(payment_terms, 0)
        if discount_percentage > max_discount:
            return jsonify({
                "error": f"Discount cannot exceed {max_discount}% for {payment_terms} payment terms"
            }), 400
        
        # Calculate extra product costs (INCL BTW voor particulieren)
        extra_cost_incl_vat = 0
        extra_items_count = 0

        for product_key, quantity in extra_products.items():
            # Ensure quantity is a valid number
            if quantity is not None and isinstance(quantity, (int, float)) and quantity > 0:
                # Remove 'extra_' prefix if present
                clean_key = product_key.replace('extra_', '') if product_key.startswith('extra_') else product_key
                product = get_product_by_key(clean_key)
                if product and 'price_incl_vat' in product:
                    extra_cost_incl_vat += product['price_incl_vat'] * quantity
                    extra_items_count += int(quantity)
        
        # Videodoorbell wordt in calculate_installation_cost functie afgehandeld
        # Hier NIET toevoegen om dubbele berekening te voorkomen
        
        # Calculate totals (INCL BTW voor particulieren)
        base_cost_incl_vat = PRICING_CONFIG['base_installation_cost_incl_vat']  # €999,99
        total_product_cost_incl_vat = base_cost_incl_vat + extra_cost_incl_vat

        # Calculate installation cost using corrected function
        from app.utils.simple_quote_products import calculate_installation_cost
        installation_cost = calculate_installation_cost(
            extra_cost_incl_vat,
            extra_items_count,
            videodoorbell_free,
            videodoorbell_paid
        )
        
        # Calculate monthly costs (INCL BTW)
        monthly_equipment = calculate_monthly_increase(extra_cost_incl_vat)
        monthly_monitoring = PRICING_CONFIG['monthly_monitoring']
        monthly_maintenance = PRICING_CONFIG['monthly_maintenance']
        total_monthly = monthly_equipment + monthly_monitoring + monthly_maintenance
        
        # Apply discount with minimum check (LEASE SYSTEEM)
        discount_amount = installation_cost * (discount_percentage / 100)
        final_installation_cost = installation_cost - discount_amount

        # Minimum installatiekosten: €749,99 (25% korting op €999,99)
        minimum_cost = PRICING_CONFIG['base_installation_cost_incl_vat'] * 0.75
        final_installation_cost = max(final_installation_cost, minimum_cost)

        # Minimum maandelijkse kosten: €49,99
        minimum_monthly = PRICING_CONFIG['base_monthly_total']
        total_monthly = max(total_monthly, minimum_monthly)
        
        return jsonify({
            "calculations": {
                "base_cost_incl_vat": base_cost_incl_vat,
                "extra_cost_incl_vat": extra_cost_incl_vat,
                "total_product_cost_incl_vat": total_product_cost_incl_vat,
                "extra_items_count": extra_items_count,
                "installation_cost_before_discount": installation_cost,
                "discount_percentage": discount_percentage,
                "discount_amount": discount_amount,
                "final_installation_cost": final_installation_cost,
                "monthly_equipment_cost": monthly_equipment,
                "monthly_monitoring_cost": monthly_monitoring,
                "monthly_maintenance_cost": monthly_maintenance,
                "total_monthly_cost": total_monthly,
                "max_discount_allowed": max_discount
            }
        }), 200
        
    except Exception as e:
        logger.error(f"Failed to calculate quote: {str(e)}")
        logger.error(f"Request data: {data}")

        # Return a safe default calculation on error
        return jsonify({
            "calculations": {
                "base_cost_excl_vat": PRICING_CONFIG['base_package_cost_excl_vat'],
                "extra_cost_excl_vat": 0,
                "total_product_cost_excl_vat": PRICING_CONFIG['base_package_cost_excl_vat'],
                "extra_items_count": 0,
                "installation_cost_before_discount": PRICING_CONFIG['base_installation_cost'],
                "discount_percentage": 0,
                "discount_amount": 0,
                "final_installation_cost": PRICING_CONFIG['base_installation_cost'],
                "monthly_equipment_cost": PRICING_CONFIG['base_monthly_equipment'],
                "monthly_monitoring_cost": PRICING_CONFIG['monthly_monitoring'],
                "monthly_maintenance_cost": PRICING_CONFIG['monthly_maintenance'],
                "total_monthly_cost": PRICING_CONFIG['base_monthly_equipment'] + PRICING_CONFIG['monthly_monitoring'] + PRICING_CONFIG['monthly_maintenance'],
                "max_discount_allowed": 25
            },
            "error": "Calculation failed, showing base package pricing"
        }), 200

@simple_quote_bp.route("/categories", methods=["GET"])
@roles_required("administrator", "verkoper")
@rate_limit("100/minute")
def get_categories():
    """
    Get available quote categories.
    
    Returns:
        JSON: Available categories with descriptions.
    """
    try:
        categories = {
            "alarm": {
                "name": "Alarm",
                "description": "Alarmsysteem voor beveiliging",
                "base_package": BASE_ALARM_PACKAGE,
                "monthly_base_cost": PRICING_CONFIG['base_monthly_equipment']
            },
            "cameras": {
                "name": "Camera's",
                "description": "Camera bewakingssysteem",
                "products": CAMERA_PRODUCTS,
                "monthly_base_cost": 0  # To be calculated based on selected cameras
            },
            "alarm_cameras": {
                "name": "Alarm + Camera's",
                "description": "Combinatie van alarm en camera systeem",
                "base_package": BASE_ALARM_PACKAGE,
                "camera_products": CAMERA_PRODUCTS,
                "monthly_base_cost": PRICING_CONFIG['base_monthly_equipment']
            }
        }
        
        return jsonify({"categories": categories}), 200

    except Exception as e:
        logger.error(f"Failed to fetch categories: {str(e)}")
        return jsonify({"error": str(e)}), 500

# Quote CRUD endpoints
@simple_quote_bp.route("/quotes", methods=["POST"])
@roles_required("administrator", "verkoper")
@rate_limit("20/minute")
def create_quote():
    """
    Create a new simple quote.

    Returns:
        JSON: Created quote data.
    """
    try:
        data = request.get_json()

        if not data:
            return jsonify({"error": "No data provided"}), 400

        # Get current user from request (set by security decorator)
        current_user = request.current_user

        quote = simple_quote_service.create_quote(data, current_user.id)

        return jsonify({"quote": quote}), 201

    except Exception as e:
        logger.error(f"Failed to create quote: {str(e)}")
        return jsonify({"error": str(e)}), 500

@simple_quote_bp.route("/quotes", methods=["GET"])
@roles_required("administrator", "verkoper")
@rate_limit("100/minute")
def get_all_quotes():
    """
    Get all quotes with pagination.

    Returns:
        JSON: List of quotes and pagination info.
    """
    try:
        page = int(request.args.get("page", 1))
        per_page = int(request.args.get("per_page", 20))

        quotes, total = simple_quote_service.get_all_quotes(page, per_page)

        return jsonify({
            "quotes": quotes,
            "total": total,
            "page": page,
            "per_page": per_page
        }), 200

    except Exception as e:
        logger.error(f"Failed to fetch quotes: {str(e)}")
        return jsonify({"error": str(e)}), 500

@simple_quote_bp.route("/quotes/<int:quote_id>", methods=["GET"])
@roles_required("administrator", "verkoper")
@rate_limit("100/minute")
def get_quote(quote_id):
    """
    Get a quote by ID.

    Args:
        quote_id: Quote ID

    Returns:
        JSON: Quote data.
    """
    try:
        quote = simple_quote_service.get_quote_by_id(quote_id)

        if not quote:
            return jsonify({"error": "Quote not found"}), 404

        return jsonify({"quote": quote}), 200

    except Exception as e:
        logger.error(f"Failed to fetch quote {quote_id}: {str(e)}")
        return jsonify({"error": str(e)}), 500

@simple_quote_bp.route("/quotes/<int:quote_id>", methods=["PUT"])
@roles_required("administrator", "verkoper")
@rate_limit("20/minute")
def update_quote(quote_id):
    """
    Update a quote.

    Args:
        quote_id: Quote ID

    Returns:
        JSON: Updated quote data.
    """
    try:
        data = request.get_json()

        if not data:
            return jsonify({"error": "No data provided"}), 400

        quote = simple_quote_service.update_quote(quote_id, data)

        return jsonify({"quote": quote}), 200

    except Exception as e:
        logger.error(f"Failed to update quote {quote_id}: {str(e)}")
        return jsonify({"error": str(e)}), 500

@simple_quote_bp.route("/quotes/<int:quote_id>/sign", methods=["POST"])
@rate_limit("10/minute")  # No role required - customers can sign
def sign_quote(quote_id):
    """
    Sign a quote.

    Args:
        quote_id: Quote ID

    Returns:
        JSON: Updated quote data.
    """
    try:
        data = request.get_json()

        if not data or 'signature_data' not in data:
            return jsonify({"error": "Signature data required"}), 400

        quote = simple_quote_service.sign_quote(quote_id, data['signature_data'])

        return jsonify({"quote": quote}), 200

    except Exception as e:
        logger.error(f"Failed to sign quote {quote_id}: {str(e)}")
        return jsonify({"error": str(e)}), 500

@simple_quote_bp.route("/quotes/<int:quote_id>/pdf", methods=["GET"])
@roles_required("administrator", "verkoper")
@rate_limit("20/minute")
def download_quote_pdf(quote_id):
    """
    Download quote as PDF.

    Args:
        quote_id: Quote ID

    Returns:
        PDF file download.
    """
    try:
        quote = simple_quote_service.get_quote_by_id(quote_id)

        if not quote:
            return jsonify({"error": "Quote not found"}), 404

        # Convert dict back to model for PDF generation
        from app.models.simple_quote import SimpleQuote
        quote_model = SimpleQuote.query.get(quote_id)

        if not quote_model:
            return jsonify({"error": "Quote not found"}), 404

        # Generate PDF
        pdf_content = pdf_service.generate_quote_pdf(quote_model)

        # Create response
        response = make_response(pdf_content)
        response.headers['Content-Type'] = 'application/pdf'
        response.headers['Content-Disposition'] = f'attachment; filename="offerte-{quote_model.quote_number or quote_id}.pdf"'

        return response

    except Exception as e:
        logger.error(f"Failed to generate PDF for quote {quote_id}: {str(e)}")
        return jsonify({"error": str(e)}), 500
