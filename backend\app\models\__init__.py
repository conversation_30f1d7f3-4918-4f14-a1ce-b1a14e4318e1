"""
Models initialization module.
This module ensures proper import order for SQLAlchemy models.
"""
# Import models in the correct order to avoid circular dependencies
from app.models.user import User
from app.models.customer import Customer
from app.models.event import Event
from app.models.document import Document

from app.models.audit_log import AuditLog
from app.models.document_template import DocumentTemplate
from app.models.customer_note import CustomerNote
from app.models.product import Product
from app.models.simple_quote import SimpleQuote
from app.models.time_entry import TimeEntry
from app.models.mileage_entry import MileageEntry
