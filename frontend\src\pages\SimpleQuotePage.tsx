import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { FaShieldAlt, FaVideo, FaCogs, FaArrowLeft, FaUser, FaPhone, FaEnvelope, FaMapMarkerAlt } from 'react-icons/fa';
import Breadcrumbs from '../components/Breadcrumbs';
import { MobileContainer, MobilePageHeader } from '../components/common/MobileUtils';

interface CustomerData {
  customer_name: string;
  customer_email: string;
  customer_phone: string;
  customer_address: string;
  customer_city: string;
  customer_postal_code: string;
}

const SimpleQuotePage: React.FC = () => {
  const navigate = useNavigate();
  const [step, setStep] = useState<'customer' | 'category'>('customer');
  const [customerData, setCustomerData] = useState<CustomerData>({
    customer_name: '',
    customer_email: '',
    customer_phone: '',
    customer_address: '',
    customer_city: '',
    customer_postal_code: ''
  });

  const categories = [
    {
      id: 'alarm',
      title: 'ALARM',
      description: 'Basis alarmsysteem - VERPLICHT minimaal pakket voor beveiliging',
      icon: <FaShieldAlt className="text-5xl text-red-500" />,
      color: 'border-red-500 hover:bg-red-50 hover:border-red-600',
      gradient: 'from-red-50 to-red-100',
      features: [
        '1x CENTRALE HUB',
        '1x BEDIENDEEL',
        '1x SIRENE',
        '2x PIRCAM',
        '2x SHOCK SENSOR',
        '1x MAGNEETCONTACT',
        '2x BRANDMELDER'
      ],
      basePrice: '€999,99',
      monthlyPrice: '€49,99',
      maxDiscount: '25% (€749,99 min)',
      popular: true
    },
    {
      id: 'cameras',
      title: 'CAMERAS',
      description: 'Camera bewakingssysteem voor visuele beveiliging',
      icon: <FaVideo className="text-5xl text-blue-500" />,
      color: 'border-blue-500 hover:bg-blue-50 hover:border-blue-600',
      gradient: 'from-blue-50 to-blue-100',
      features: [
        'IP Camera\'s binnen/buiten',
        'Network Video Recorder',
        'Mobiele app toegang',
        'Cloud opslag optie',
        'Nachtzicht functie',
        '+ Video Deurbel optie'
      ],
      basePrice: 'Op maat',
      monthlyPrice: 'Op maat',
      maxDiscount: 'Variabel',
      popular: false
    },
    {
      id: 'alarm_cameras',
      title: 'ALARM + CAMERAS',
      description: 'Complete beveiligingsoplossing - Alarm basis + Camera systeem',
      icon: <FaCogs className="text-5xl text-green-500" />,
      color: 'border-green-500 hover:bg-green-50 hover:border-green-600',
      gradient: 'from-green-50 to-green-100',
      features: [
        'Volledige ALARM basis pakket',
        'IP Camera systeem',
        'Geïntegreerde bediening',
        'Mobiele app controle',
        'Professionele monitoring',
        '+ Video Deurbel optie'
      ],
      basePrice: 'Vanaf €999,99',
      monthlyPrice: 'Vanaf €49,99',
      maxDiscount: '25% op alarm deel',
      popular: false
    }
  ];

  const handleCustomerSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!customerData.customer_name.trim()) {
      alert('Naam is verplicht');
      return;
    }
    setStep('category');
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setCustomerData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleCategorySelect = (categoryId: string) => {
    // Navigate to quote builder with customer data and selected category
    navigate('/simple-quotes/builder', {
      state: {
        customerData,
        category: categoryId
      }
    });
  };

  if (step === 'customer') {
    return (
      <MobileContainer>
        <Breadcrumbs />

        <MobilePageHeader
          title="Nieuwe Offerte"
          subtitle="Vul eerst uw contactgegevens in voor een persoonlijke offerte"
        />

        <div className="space-y-6">
          {/* Back button */}
          <div className="flex items-center">
            <button
              onClick={() => navigate(-1)}
              className="flex items-center text-gray-600 hover:text-gray-800 transition-colors"
            >
              <FaArrowLeft className="mr-2" />
              Terug
            </button>
          </div>

          {/* Progress indicator */}
          <div className="flex items-center justify-center space-x-4 mb-8">
            <div className="flex items-center">
              <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium">
                1
              </div>
              <span className="ml-2 text-sm font-medium text-blue-600">Klantgegevens</span>
            </div>
            <div className="w-8 h-0.5 bg-gray-300"></div>
            <div className="flex items-center">
              <div className="w-8 h-8 bg-gray-300 text-gray-500 rounded-full flex items-center justify-center text-sm font-medium">
                2
              </div>
              <span className="ml-2 text-sm text-gray-500">Systeem kiezen</span>
            </div>
            <div className="w-8 h-0.5 bg-gray-300"></div>
            <div className="flex items-center">
              <div className="w-8 h-8 bg-gray-300 text-gray-500 rounded-full flex items-center justify-center text-sm font-medium">
                3
              </div>
              <span className="ml-2 text-sm text-gray-500">Configureren</span>
            </div>
          </div>

          {/* Customer form */}
          <div className="bg-white rounded-xl shadow-sm border p-6">
            <div className="flex items-center mb-6">
              <FaUser className="text-blue-600 text-xl mr-3" />
              <h3 className="text-xl font-semibold text-gray-900">Uw contactgegevens</h3>
            </div>

            <form onSubmit={handleCustomerSubmit} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <FaUser className="inline mr-2 text-gray-400" />
                    Volledige naam *
                  </label>
                  <input
                    type="text"
                    name="customer_name"
                    value={customerData.customer_name}
                    onChange={handleInputChange}
                    required
                    placeholder="Bijv. Jan de Vries"
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <FaEnvelope className="inline mr-2 text-gray-400" />
                    Email adres
                  </label>
                  <input
                    type="email"
                    name="customer_email"
                    value={customerData.customer_email}
                    onChange={handleInputChange}
                    placeholder="bijv. <EMAIL>"
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <FaPhone className="inline mr-2 text-gray-400" />
                    Telefoonnummer
                  </label>
                  <input
                    type="tel"
                    name="customer_phone"
                    value={customerData.customer_phone}
                    onChange={handleInputChange}
                    placeholder="06-12345678"
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <FaMapMarkerAlt className="inline mr-2 text-gray-400" />
                    Postcode
                  </label>
                  <input
                    type="text"
                    name="customer_postal_code"
                    value={customerData.customer_postal_code}
                    onChange={handleInputChange}
                    placeholder="1234 AB"
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Plaats
                  </label>
                  <input
                    type="text"
                    name="customer_city"
                    value={customerData.customer_city}
                    onChange={handleInputChange}
                    placeholder="Amsterdam"
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Straat en huisnummer
                  </label>
                  <input
                    type="text"
                    name="customer_address"
                    value={customerData.customer_address}
                    onChange={handleInputChange}
                    placeholder="Hoofdstraat 123"
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors"
                  />
                </div>
              </div>

              <div className="flex justify-end pt-4">
                <button
                  type="submit"
                  className="bg-blue-600 text-white px-8 py-3 rounded-lg hover:bg-blue-700 transition-colors font-medium flex items-center"
                >
                  Verder naar systeem keuze
                  <FaArrowLeft className="ml-2 rotate-180" />
                </button>
              </div>
            </form>
          </div>

          {/* Info section */}
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-xl p-6">
            <h4 className="font-semibold text-blue-900 mb-3 flex items-center">
              <FaShieldAlt className="mr-2" />
              Waarom eerst uw gegevens?
            </h4>
            <ul className="text-sm text-blue-800 space-y-2">
              <li className="flex items-start">
                <span className="w-2 h-2 bg-blue-500 rounded-full mr-3 mt-2 flex-shrink-0"></span>
                Uw gegevens worden automatisch in de offerte verwerkt
              </li>
              <li className="flex items-start">
                <span className="w-2 h-2 bg-blue-500 rounded-full mr-3 mt-2 flex-shrink-0"></span>
                Persoonlijke offerte met uw naam en adres
              </li>
              <li className="flex items-start">
                <span className="w-2 h-2 bg-blue-500 rounded-full mr-3 mt-2 flex-shrink-0"></span>
                Sneller proces zonder dubbel invoeren
              </li>
              <li className="flex items-start">
                <span className="w-2 h-2 bg-blue-500 rounded-full mr-3 mt-2 flex-shrink-0"></span>
                Uw gegevens worden veilig opgeslagen
              </li>
            </ul>
          </div>
        </div>
      </MobileContainer>
    );
  }

  // Category selection step
  return (
    <MobileContainer>
      <Breadcrumbs />

      <MobilePageHeader
        title={`Hallo ${customerData.customer_name}!`}
        subtitle="Kies het beveiligingssysteem dat het beste bij u past"
      />

      <div className="space-y-6">
        {/* Back button */}
        <div className="flex items-center justify-between">
          <button
            onClick={() => setStep('customer')}
            className="flex items-center text-gray-600 hover:text-gray-800 transition-colors"
          >
            <FaArrowLeft className="mr-2" />
            Gegevens wijzigen
          </button>

          {/* Progress indicator */}
          <div className="flex items-center space-x-2">
            <div className="flex items-center">
              <div className="w-6 h-6 bg-green-500 text-white rounded-full flex items-center justify-center text-xs">
                ✓
              </div>
              <span className="ml-1 text-xs text-green-600">Gegevens</span>
            </div>
            <div className="w-4 h-0.5 bg-green-500"></div>
            <div className="flex items-center">
              <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs">
                2
              </div>
              <span className="ml-1 text-xs text-blue-600">Systeem</span>
            </div>
          </div>
        </div>

        {/* Category selection */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {categories.map((category) => (
            <div
              key={category.id}
              onClick={() => handleCategorySelect(category.id)}
              className={`
                relative border-2 rounded-xl p-6 cursor-pointer transition-all duration-300
                ${category.color}
                hover:shadow-xl transform hover:-translate-y-2
                bg-gradient-to-br ${category.gradient}
                ${category.popular ? 'ring-2 ring-yellow-400 ring-opacity-50' : ''}
              `}
            >
              {category.popular && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <span className="bg-yellow-400 text-yellow-900 px-3 py-1 rounded-full text-xs font-bold">
                    POPULAIR
                  </span>
                </div>
              )}

              <div className="text-center mb-6">
                <div className="mb-4 flex justify-center">
                  {category.icon}
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-2">
                  {category.title}
                </h3>
                <p className="text-gray-600 text-sm mb-4">
                  {category.description}
                </p>
              </div>

              {/* Features list */}
              <div className="mb-6">
                <h4 className="font-semibold text-gray-800 mb-3">Inclusief:</h4>
                <ul className="text-sm text-gray-600 space-y-2">
                  {category.features.map((feature, index) => (
                    <li key={index} className="flex items-center">
                      <span className="w-2 h-2 bg-green-500 rounded-full mr-3 flex-shrink-0"></span>
                      {feature}
                    </li>
                  ))}
                </ul>
              </div>

              {/* Pricing */}
              <div className="border-t border-gray-200 pt-4 mb-4">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm text-gray-600">Installatiekosten:</span>
                  <span className="font-bold text-lg text-gray-900">{category.basePrice}</span>
                </div>
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm text-gray-600">Maandelijkse lease:</span>
                  <span className="font-bold text-lg text-green-600">{category.monthlyPrice}</span>
                </div>
                {category.maxDiscount && (
                  <div className="flex justify-between items-center">
                    <span className="text-xs text-gray-500">Max korting:</span>
                    <span className="text-xs text-orange-600 font-medium">{category.maxDiscount}</span>
                  </div>
                )}
              </div>

              {/* Call to action */}
              <div className="text-center">
                <div className="bg-white bg-opacity-80 rounded-lg py-2 px-4">
                  <span className="text-sm font-medium text-gray-800">
                    Klik om te configureren →
                  </span>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Additional info */}
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-xl p-6">
          <h4 className="font-semibold text-blue-900 mb-3">Belangrijke informatie:</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-blue-800">
            <ul className="space-y-2">
              <li className="flex items-start">
                <span className="w-2 h-2 bg-blue-500 rounded-full mr-3 mt-2 flex-shrink-0"></span>
                Alle prijzen zijn inclusief BTW
              </li>
              <li className="flex items-start">
                <span className="w-2 h-2 bg-blue-500 rounded-full mr-3 mt-2 flex-shrink-0"></span>
                Installatiekosten zijn eenmalig
              </li>
              <li className="flex items-start">
                <span className="w-2 h-2 bg-blue-500 rounded-full mr-3 mt-2 flex-shrink-0"></span>
                Maandelijks: apparatuur + meldkamer + onderhoud
              </li>
            </ul>
            <ul className="space-y-2">
              <li className="flex items-start">
                <span className="w-2 h-2 bg-blue-500 rounded-full mr-3 mt-2 flex-shrink-0"></span>
                Tot 25% korting bij betaling in 1 termijn
              </li>
              <li className="flex items-start">
                <span className="w-2 h-2 bg-blue-500 rounded-full mr-3 mt-2 flex-shrink-0"></span>
                Video deurbel kan gratis toegevoegd worden
              </li>
              <li className="flex items-start">
                <span className="w-2 h-2 bg-blue-500 rounded-full mr-3 mt-2 flex-shrink-0"></span>
                Professionele installatie inbegrepen
              </li>
            </ul>
          </div>
        </div>

        {/* Contact info */}
        <div className="text-center text-gray-600 text-sm bg-gray-50 rounded-lg p-4">
          <p className="mb-1">Vragen over de offerte?</p>
          <p className="font-medium text-gray-800">Tel: 020-1234567 | Email: <EMAIL></p>
        </div>
      </div>
    </MobileContainer>
  );
};

export default SimpleQuotePage;
