"""
AMSPM Lease Systeem - Product Configuratie
===========================================

LEASE SYSTEEM SPECIFICATIES:
- Klanten betalen eenmalige installatiekosten (€999,99 basis, max 25% korting)
- Klanten betalen maandelijkse lease (€49,99 minimum)
- Producten blijven eigendom van AMSPM

BASIS ALARM PAKKET (VERPLICHT):
1x CENTRALE HUB, 1x BEDIENDEEL, 1x SIRENE, 2x PIRCAM,
2x SHOCK SENSOR, 1x MAGNEETCONTACT, 2x BRANDMELDER
Totaal: €554,48 ex BTW → €999,99 incl BTW installatiekosten

CATEGORIEËN:
- ALARM: Basis alarmsysteem
- CAMERAS: Camera systeem
- ALARM + CAMERAS: Combinatie systeem

VIDEODEURBEL: €249,99 → kan gratis gemaakt worden bij alle categorieën
"""

# Base ALARM package products (VERPLICHT in elke alarm offerte)
BASE_ALARM_PACKAGE = {
    'centrale_hub': {
        'name': 'CENTRALE HUB',
        'description': 'Centrale hub voor het alarmsysteem',
        'quantity': 1,
        'price_excl_vat': 106.26,
        'price_incl_vat': 128.57,
        'image': 'Alarmsysteem/Centrale Hub 890001.jpg'
    },
    'bediendeel': {
        'name': 'BEDIENDEEL',
        'description': 'Bedieningspaneel voor het systeem',
        'quantity': 1,
        'price_excl_vat': 46.00,
        'price_incl_vat': 55.66,
        'image': 'Alarmsysteem/Code bediendeel 890020.jpg'
    },
    'sirene': {
        'name': 'SIRENE',
        'description': 'Externe sirene',
        'quantity': 1,
        'price_excl_vat': 26.68,
        'price_incl_vat': 32.28,
        'image': 'Alarmsysteem/Sirene 890017.jpg'
    },
    'pircam': {
        'name': 'PIRCAM',
        'description': 'PIR sensor met camera',
        'quantity': 2,
        'price_excl_vat': 73.60,
        'price_incl_vat': 89.06,
        'image': 'Alarmsysteem/PIR Cam 890006.jpg'
    },
    'shock_sensor': {
        'name': 'SHOCK SENSOR',
        'description': 'Trillingssensor voor ramen/deuren',
        'quantity': 2,
        'price_excl_vat': 22.08,
        'price_incl_vat': 26.72,
        'image': 'Alarmsysteem/Shock sensor 890008.jpg'
    },
    'magneetcontact': {
        'name': 'MAGNEETCONTACT',
        'description': 'Deur/raam sensor',
        'quantity': 1,
        'price_excl_vat': 16.10,
        'price_incl_vat': 19.48,
        'image': 'Alarmsysteem/Magneetcontact 890026.jpg'
    },
    'brandmelder': {
        'name': 'BRANDMELDER',
        'description': 'Rookmelder voor branddetectie',
        'quantity': 2,
        'price_excl_vat': 34.04,
        'price_incl_vat': 41.19,
        'image': 'Alarmsysteem/Brandmelder 890010.png'
    }
}

# Extra products that can be added
EXTRA_PRODUCTS = {
    'magneetcontact': {
        'name': 'Extra Magneetcontact',
        'description': 'Extra deur/raam sensor',
        'price_excl_vat': 16.10,
        'price_incl_vat': 19.48,
        'image': 'Alarmsysteem/Magneetcontact 890026.jpg'
    },
    'shock_sensor': {
        'name': 'Extra Shock Sensor',
        'description': 'Extra trillingssensor',
        'price_excl_vat': 22.08,
        'price_incl_vat': 26.72,
        'image': 'Alarmsysteem/Shock sensor 890008.jpg'
    },
    'pir_normaal': {
        'name': 'PIR Sensor',
        'description': 'Bewegingssensor (zonder camera)',
        'price_excl_vat': 29.44,
        'price_incl_vat': 35.62,
        'image': 'Alarmsysteem/PIR 890003.jpg'
    },
    'rookmelder': {
        'name': 'Extra Rookmelder',
        'description': 'Extra brandmelder',
        'price_excl_vat': 34.04,
        'price_incl_vat': 41.19,
        'image': 'Alarmsysteem/Brandmelder 890010.png'
    },
    'pircam': {
        'name': 'Extra PIR Camera',
        'description': 'Extra PIR sensor met camera',
        'price_excl_vat': 73.60,
        'price_incl_vat': 89.06,
        'image': 'Alarmsysteem/PIR Cam 890006.jpg'
    },
    'bediendeel': {
        'name': 'Extra Bediendeel',
        'description': 'Extra bedieningspaneel',
        'price_excl_vat': 46.00,
        'price_incl_vat': 55.66,
        'image': 'Alarmsysteem/Code bediendeel 890020.jpg'
    },
    'sirene': {
        'name': 'Extra Sirene',
        'description': 'Extra externe sirene',
        'price_excl_vat': 26.68,
        'price_incl_vat': 32.28,
        'image': 'Alarmsysteem/Sirene 890017.jpg'
    },
    'videodoorbell': {
        'name': 'VIDEO DEURBEL',
        'description': 'Slimme video deurbel (kan gratis gemaakt worden)',
        'price_excl_vat': 206.61,
        'price_incl_vat': 249.99,
        'image': 'Videodeurbel/Videodeurbel wit 963104.jpg'
    }
}

# Camera products (for camera-only quotes)
CAMERA_PRODUCTS = {
    'camera_indoor': {
        'name': 'Binnen Camera',
        'description': 'IP camera voor binnen',
        'price_excl_vat': 89.00,
        'price_incl_vat': 107.69,
        'image': 'Camerasysteem/Eyeball 4MP 962681.jpg'
    },
    'camera_outdoor': {
        'name': 'Buiten Camera',
        'description': 'Weerbestendige IP camera',
        'price_excl_vat': 129.00,
        'price_incl_vat': 156.09,
        'image': 'Camerasysteem/Eyeball 4MP 962681.jpg'
    },
    'nvr_4ch': {
        'name': 'NVR 4 Kanalen',
        'description': 'Network Video Recorder voor 4 camera\'s',
        'price_excl_vat': 199.00,
        'price_incl_vat': 240.79,
        'image': 'Camerasysteem/Recorder 967624.jpg'
    },
    'nvr_8ch': {
        'name': 'NVR 8 Kanalen',
        'description': 'Network Video Recorder voor 8 camera\'s',
        'price_excl_vat': 299.00,
        'price_incl_vat': 361.79,
        'image': 'Camerasysteem/Recorder 967624.jpg'
    }
}

# Pricing constants - LEASE SYSTEEM
PRICING_CONFIG = {
    # Basis pakket kosten (ex BTW)
    'base_package_cost_excl_vat': 554.48,  # Totaal van alle basis producten

    # Installatiekosten (incl BTW) - Dit is wat klanten betalen
    'base_installation_cost_incl_vat': 999.99,  # Basis installatiekosten
    'base_installation_cost_excl_vat': 826.44,  # 999.99 / 1.21

    # Maandelijkse kosten (incl BTW)
    'base_monthly_total': 49.99,           # MINIMUM maandelijkse kosten
    'base_monthly_equipment': 32.99,       # Apparatuur lease
    'monthly_monitoring': 8.50,            # Meldkamer
    'monthly_maintenance': 8.50,           # Onderhoud

    # Berekeningsparameters
    'labor_per_extra_item': 10.0,          # €10 per extra artikel (installatie)
    'margin_percentage': 11.55,            # 11,55% marge op producten
    'vat_percentage': 21.0,                # BTW percentage

    # Videodeurbel
    'videodoorbell_price_excl_vat': 206.61,
    'videodoorbell_price_incl_vat': 249.99
}

# Discount limits based on payment terms
DISCOUNT_LIMITS = {
    '1_term': 25.0,
    '2_terms': 15.0,
    '3_terms': 10.0
}

def get_base_package_total():
    """Calculate total cost of base package."""
    total = 0
    for product_key, product in BASE_ALARM_PACKAGE.items():
        total += product['price_excl_vat'] * product['quantity']
    return total

def get_product_by_key(product_key):
    """Get product information by key."""
    if product_key in EXTRA_PRODUCTS:
        return EXTRA_PRODUCTS[product_key]
    elif product_key in CAMERA_PRODUCTS:
        return CAMERA_PRODUCTS[product_key]
    elif product_key in BASE_ALARM_PACKAGE:
        return BASE_ALARM_PACKAGE[product_key]
    return None

def get_products_for_category(category):
    """Get available products for a specific category."""
    if category == 'alarm':
        return {**BASE_ALARM_PACKAGE, **EXTRA_PRODUCTS}
    elif category == 'cameras':
        return {**CAMERA_PRODUCTS, 'videodoorbell': EXTRA_PRODUCTS['videodoorbell']}
    elif category == 'alarm_cameras':
        return {**BASE_ALARM_PACKAGE, **EXTRA_PRODUCTS, **CAMERA_PRODUCTS}
    return {}

def calculate_monthly_increase(extra_cost_incl_vat):
    """
    Calculate monthly cost increase based on extra products - PARTICULIEREN (INCL BTW)

    Berekeningslogica:
    - Basis apparatuur kost €32,99/maand (bij €670,92 incl BTW = 100%)
    - Extra producten verhogen percentage van totaal
    - Voorbeeld: €89,06 extra incl BTW = 13,3% van €670,92 → maandelijkse kosten stijgen met 13,3%
    """
    # Basis pakket kosten incl BTW (€554,48 ex BTW * 1.21 = €670,92)
    base_cost_incl_vat = PRICING_CONFIG['base_package_cost_excl_vat'] * (1 + PRICING_CONFIG['vat_percentage'] / 100)
    base_monthly = PRICING_CONFIG['base_monthly_equipment']   # €32,99

    if base_cost_incl_vat > 0:
        # Bereken nieuwe totaal kosten
        total_cost = base_cost_incl_vat + extra_cost_incl_vat
        # Bereken percentage stijging
        percentage_increase = total_cost / base_cost_incl_vat
        # Pas toe op maandelijkse kosten
        return base_monthly * percentage_increase
    return base_monthly

def calculate_installation_cost(extra_cost_incl_vat, extra_items_count, videodoorbell_free=False, videodoorbell_paid=False):
    """
    Calculate total installation cost - PARTICULIEREN (ALLES INCL BTW)

    LEASE SYSTEEM BEREKENINGSLOGICA:
    1. BASIS: €999,99 incl BTW (VAST - dit is de basis installatie)
    2. EXTRA PRODUCTEN: prijs incl BTW + 11,55% marge + €10 arbeid per item
    3. VIDEODEURBEL: €249,99 incl BTW → kan gratis gemaakt worden

    Voorbeeld:
    - Basis: €999,99
    - 1 extra PIRCAM: €89,06 incl BTW + 11,55% + €10 = €109,35
    - Totaal: €999,99 + €109,35 = €1.109,34
    """
    # Start met VASTE basis installatiekosten
    total_cost_incl_vat = PRICING_CONFIG['base_installation_cost_incl_vat']  # €999,99

    # Bereken extra kosten (alles incl BTW voor particulieren)
    if extra_cost_incl_vat > 0:
        # Extra producten (al incl BTW) + marge
        extra_with_margin = extra_cost_incl_vat * (1 + PRICING_CONFIG['margin_percentage'] / 100)
        # Voeg arbeid toe
        extra_labor = extra_items_count * PRICING_CONFIG['labor_per_extra_item']

        total_cost_incl_vat += extra_with_margin + extra_labor

    # Videodeurbel logica
    if videodoorbell_paid and not videodoorbell_free:
        # Betaalde videodeurbel: €249,99 incl BTW + 11,55% marge + €10 arbeid
        videodoorbell_with_margin = PRICING_CONFIG['videodoorbell_price_incl_vat'] * (1 + PRICING_CONFIG['margin_percentage'] / 100)
        total_cost_incl_vat += videodoorbell_with_margin + PRICING_CONFIG['labor_per_extra_item']
    elif videodoorbell_free:
        # Gratis videodeurbel - alleen arbeid kosten
        total_cost_incl_vat += PRICING_CONFIG['labor_per_extra_item']  # €10 arbeid

    return total_cost_incl_vat
