import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { FaArrowLeft, FaPlus, FaMinus, FaCalculator, FaUser, <PERSON>a<PERSON>ye, <PERSON>a<PERSON><PERSON><PERSON>, FaVideo } from 'react-icons/fa';
import Breadcrumbs from '../components/Breadcrumbs';
import { MobileContainer, MobilePageHeader } from '../components/common/MobileUtils';
import LoadingSpinner from '../components/LoadingSpinner';
import simpleQuoteService, { Product, QuoteCalculation } from '../services/simpleQuoteService';

interface CustomerData {
  customer_name: string;
  customer_email: string;
  customer_phone: string;
  customer_address: string;
  customer_city: string;
  customer_postal_code: string;
}

const SimpleQuoteBuilderPage: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();

  // Get customer data and category from previous page
  const { customerData, category } = location.state || {};

  // Redirect if no data
  if (!customerData || !category) {
    navigate('/simple-quotes');
    return null;
  }

  const [loading, setLoading] = useState(false);
  const [products, setProducts] = useState<Record<string, Product>>({});
  const [calculations, setCalculations] = useState<QuoteCalculation | null>(null);
  
  // Product quantities
  const [extraProducts, setExtraProducts] = useState({
    extra_magneetcontact: 0,
    extra_shock_sensor: 0,
    extra_pir_normaal: 0,
    extra_rookmelder: 0,
    extra_pircam: 0,
    extra_bediendeel: 0,
    extra_sirene: 0
  });

  // Quote settings
  const [paymentTerms, setPaymentTerms] = useState('1_term');
  const [discountPercentage, setDiscountPercentage] = useState(0);
  const [videodoorbellFree, setVideodoorbellFree] = useState(false);
  const [videodoorbellPaid, setVideodoorbellPaid] = useState(false);

  // Discount limits based on payment terms
  const getMaxDiscountForTerms = (terms: string) => {
    switch (terms) {
      case '1_term': return 25;
      case '2_terms': return 15;
      case '3_terms': return 10;
      default: return 0;
    }
  };

  useEffect(() => {
    fetchProducts();
  }, [category]);

  useEffect(() => {
    // Always calculate quote when dependencies change
    calculateQuote();
  }, [extraProducts, paymentTerms, discountPercentage, videodoorbellFree, videodoorbellPaid]);

  const fetchProducts = async () => {
    try {
      setLoading(true);
      const data = await simpleQuoteService.getProducts(category);
      console.log('Products data:', data); // Debug log

      // Handle different response structures
      let extraProducts = {};
      if (data.products) {
        if (data.products.extra_products) {
          extraProducts = data.products.extra_products;
        } else if (data.products.base_package && data.products.extra_products) {
          // New structured response
          extraProducts = data.products.extra_products;
        } else if (typeof data.products === 'object') {
          // Fallback: Filter out base package items, keep only extra products
          const productKeys = Object.keys(data.products);
          productKeys.forEach(key => {
            // Include products that are typically extra items
            if (key.includes('extra_') ||
                ['magneetcontact', 'shock_sensor', 'pir_normaal', 'rookmelder',
                 'pircam', 'bediendeel', 'sirene', 'videodoorbell'].includes(key)) {
              extraProducts[key] = data.products[key];
            }
          });
        }
      }

      console.log('Extra products:', extraProducts); // Debug log
      setProducts(extraProducts);

      // Always calculate quote, even with no extra products
      calculateQuote();
    } catch (error) {
      console.error('Error fetching products:', error);
      // Still calculate quote with base package
      calculateQuote();
    } finally {
      setLoading(false);
    }
  };

  const calculateQuote = async () => {
    try {
      const data = await simpleQuoteService.calculateQuote({
        extra_products: extraProducts,
        payment_terms: paymentTerms,
        discount_percentage: discountPercentage,
        videodoorbell_free: videodoorbellFree,
        videodoorbell_paid: videodoorbellPaid
      });

      setCalculations(data.calculations);
    } catch (error) {
      console.error('Error calculating quote:', error);
    }
  };

  const updateProductQuantity = (productKey: string, change: number) => {
    setExtraProducts(prev => ({
      ...prev,
      [productKey]: Math.max(0, prev[productKey as keyof typeof prev] + change)
    }));
  };

  const handlePaymentTermsChange = (terms: string) => {
    setPaymentTerms(terms);
    // Reset discount when payment terms change
    const maxDiscount = getMaxDiscountForTerms(terms);
    setDiscountPercentage(Math.min(discountPercentage, maxDiscount));
  };

  const handleDiscountChange = (value: number) => {
    const maxDiscount = getMaxDiscountForTerms(paymentTerms);
    setDiscountPercentage(Math.min(value, maxDiscount));
  };

  const getPaymentTermsLabel = (terms: string) => {
    switch (terms) {
      case '1_term': return '1 termijn';
      case '2_terms': return '2 termijnen';
      case '3_terms': return '3 termijnen';
      default: return terms;
    }
  };

  const getCategoryTitle = () => {
    switch (category) {
      case 'alarm': return 'Alarm Systeem';
      case 'cameras': return 'Camera Systeem';
      case 'alarm_cameras': return 'Alarm + Camera Systeem';
      default: return 'Offerte';
    }
  };

  const handleContinue = () => {
    // Navigate to signature page with all data
    const config = {
      customerData,
      category,
      extraProducts,
      paymentTerms,
      discountPercentage,
      videodoorbellFree,
      videodoorbellPaid,
      calculations
    };

    navigate('/simple-quotes/signature', {
      state: { quoteConfig: config }
    });
  };

  if (loading) {
    return <LoadingSpinner />;
  }

  return (
    <MobileContainer>
      <Breadcrumbs />

      <MobilePageHeader
        title={`${getCategoryTitle()} Configuratie`}
        subtitle={`Personaliseer uw offerte, ${customerData.customer_name}`}
      />

      <div className="space-y-6">
        {/* Header with customer info and progress */}
        <div className="flex items-center justify-between">
          <button
            onClick={() => navigate('/simple-quotes')}
            className="flex items-center text-gray-600 hover:text-gray-800 transition-colors"
          >
            <FaArrowLeft className="mr-2" />
            Terug
          </button>

          {/* Progress indicator */}
          <div className="flex items-center space-x-2">
            <div className="flex items-center">
              <div className="w-6 h-6 bg-green-500 text-white rounded-full flex items-center justify-center text-xs">
                ✓
              </div>
              <span className="ml-1 text-xs text-green-600">Gegevens</span>
            </div>
            <div className="w-4 h-0.5 bg-green-500"></div>
            <div className="flex items-center">
              <div className="w-6 h-6 bg-green-500 text-white rounded-full flex items-center justify-center text-xs">
                ✓
              </div>
              <span className="ml-1 text-xs text-green-600">Systeem</span>
            </div>
            <div className="w-4 h-0.5 bg-blue-500"></div>
            <div className="flex items-center">
              <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs">
                3
              </div>
              <span className="ml-1 text-xs text-blue-600">Config</span>
            </div>
          </div>
        </div>

        {/* Customer summary card */}
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-xl p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <FaUser className="text-blue-600 mr-3" />
              <div>
                <h3 className="font-semibold text-blue-900">{customerData.customer_name}</h3>
                <p className="text-sm text-blue-700">
                  {customerData.customer_address && customerData.customer_city
                    ? `${customerData.customer_address}, ${customerData.customer_city}`
                    : customerData.customer_email || customerData.customer_phone
                  }
                </p>
              </div>
            </div>
            <div className="text-right">
              <p className="text-sm font-medium text-blue-900">{getCategoryTitle()}</p>
              <p className="text-xs text-blue-700">Gekozen systeem</p>
            </div>
          </div>
        </div>

        {/* Base package info */}
        <div className="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-xl p-6">
          <h3 className="font-semibold text-green-900 mb-4 flex items-center">
            <FaCheck className="mr-2 text-green-600" />
            {getCategoryTitle()} - Basis Pakket (inbegrepen)
          </h3>
          {category === 'alarm' && (
            <div className="grid grid-cols-2 md:grid-cols-3 gap-3 text-sm text-green-800">
              <div className="flex items-center">
                <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                1x Centrale Hub
              </div>
              <div className="flex items-center">
                <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                1x Bediendeel
              </div>
              <div className="flex items-center">
                <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                1x Sirene
              </div>
              <div className="flex items-center">
                <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                2x PIR Camera
              </div>
              <div className="flex items-center">
                <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                2x Shock Sensor
              </div>
              <div className="flex items-center">
                <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                1x Magneetcontact
              </div>
              <div className="flex items-center">
                <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                2x Brandmelder
              </div>
            </div>
          )}
          {category === 'cameras' && (
            <div className="grid grid-cols-2 md:grid-cols-3 gap-3 text-sm text-green-800">
              <div className="flex items-center">
                <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                IP Camera's binnen/buiten
              </div>
              <div className="flex items-center">
                <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                Network Video Recorder
              </div>
              <div className="flex items-center">
                <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                Mobiele app toegang
              </div>
              <div className="flex items-center">
                <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                Cloud opslag optie
              </div>
              <div className="flex items-center">
                <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                Nachtzicht functie
              </div>
            </div>
          )}
          {category === 'alarm_cameras' && (
            <div className="grid grid-cols-2 md:grid-cols-3 gap-3 text-sm text-green-800">
              <div className="flex items-center">
                <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                Volledige alarmsysteem
              </div>
              <div className="flex items-center">
                <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                IP Camera systeem
              </div>
              <div className="flex items-center">
                <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                Geïntegreerde bediening
              </div>
              <div className="flex items-center">
                <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                Mobiele app controle
              </div>
              <div className="flex items-center">
                <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                Professionele monitoring
              </div>
            </div>
          )}
        </div>

        {/* Extra products */}
        <div className="bg-white rounded-xl shadow-sm border p-6">
          <h3 className="font-semibold text-gray-900 mb-6 flex items-center text-lg">
            <FaPlus className="mr-3 text-blue-600" />
            Extra Producten Toevoegen
          </h3>

          {Object.keys(products).length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <FaEye className="mx-auto text-3xl mb-3" />
              <p>Geen extra producten beschikbaar voor dit systeem</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {Object.entries(products).map(([key, product]) => (
                <div key={key} className="border border-gray-200 rounded-xl p-4 hover:shadow-md transition-shadow">
                  <div className="flex items-start space-x-4">
                    {/* Product image */}
                    <div className="w-20 h-20 bg-gray-100 rounded-xl flex items-center justify-center overflow-hidden flex-shrink-0">
                      <img
                        src={simpleQuoteService.getProductImageUrl(product.image)}
                        alt={product.name}
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0yMCAyMEg0NFY0NEgyMFYyMFoiIHN0cm9rZT0iIzlDQTNBRiIgc3Ryb2tlLXdpZHRoPSIyIiBmaWxsPSJub25lIi8+CjxjaXJjbGUgY3g9IjI4IiBjeT0iMjgiIHI9IjMiIGZpbGw9IiM5Q0EzQUYiLz4KPHBhdGggZD0iTTIwIDM2TDI4IDI4TDM2IDM2TDQ0IDI4VjQ0SDIwVjM2WiIgZmlsbD0iIzlDQTNBRiIvPgo8L3N2Zz4K';
                        }}
                      />
                    </div>

                    <div className="flex-1 min-w-0">
                      <h4 className="font-semibold text-gray-900 mb-1">{product.name}</h4>
                      <p className="text-sm text-gray-600 mb-2 line-clamp-2">{product.description}</p>
                      <p className="text-lg font-bold text-green-600">€{product.price_incl_vat.toFixed(2)}</p>
                    </div>
                  </div>

                  {/* Quantity controls */}
                  <div className="flex items-center justify-between mt-4 pt-4 border-t border-gray-100">
                    <span className="text-sm font-medium text-gray-700">Aantal:</span>
                    <div className="flex items-center space-x-3">
                      <button
                        onClick={() => updateProductQuantity(key, -1)}
                        className="w-10 h-10 rounded-full bg-red-100 text-red-600 flex items-center justify-center hover:bg-red-200 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                        disabled={extraProducts[key as keyof typeof extraProducts] === 0}
                      >
                        <FaMinus size={14} />
                      </button>

                      <span className="w-12 text-center font-bold text-lg">
                        {extraProducts[key as keyof typeof extraProducts]}
                      </span>

                      <button
                        onClick={() => updateProductQuantity(key, 1)}
                        className="w-10 h-10 rounded-full bg-green-100 text-green-600 flex items-center justify-center hover:bg-green-200 transition-colors"
                      >
                        <FaPlus size={14} />
                      </button>
                    </div>
                  </div>

                  {/* Total for this product */}
                  {extraProducts[key as keyof typeof extraProducts] > 0 && (
                    <div className="mt-3 pt-3 border-t border-gray-100">
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-600">Subtotaal:</span>
                        <span className="font-bold text-gray-900">
                          €{(product.price_incl_vat * extraProducts[key as keyof typeof extraProducts]).toFixed(2)}
                        </span>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Video doorbell - Beschikbaar bij alle categorieën */}
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border-2 border-blue-200 p-4">
          <div className="flex items-center mb-3">
            <FaVideo className="text-blue-500 mr-2" />
            <h3 className="font-semibold text-gray-900">VIDEO DEURBEL</h3>
            <span className="ml-2 bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
              Bij alle categorieën
            </span>
          </div>

          <p className="text-sm text-gray-600 mb-4">
            Slimme video deurbel - Normaal €249,99, kan gratis gemaakt worden!
          </p>

          <div className="space-y-3">
            <label className="flex items-center p-2 rounded-lg hover:bg-white hover:bg-opacity-50 transition-colors cursor-pointer">
              <input
                type="radio"
                name="videodoorbell"
                checked={!videodoorbellFree && !videodoorbellPaid}
                onChange={() => {
                  setVideodoorbellFree(false);
                  setVideodoorbellPaid(false);
                }}
                className="mr-3 text-blue-600"
              />
              <span className="text-gray-700">Geen video deurbel</span>
            </label>

            <label className="flex items-center p-2 rounded-lg hover:bg-white hover:bg-opacity-50 transition-colors cursor-pointer border-2 border-green-200 bg-green-50">
              <input
                type="radio"
                name="videodoorbell"
                checked={videodoorbellFree}
                onChange={() => {
                  setVideodoorbellFree(true);
                  setVideodoorbellPaid(false);
                }}
                className="mr-3 text-green-600"
              />
              <div className="flex-1">
                <span className="text-green-700 font-semibold">🎁 GRATIS Video Deurbel</span>
                <div className="text-sm text-green-600">
                  Normale waarde €249,99 → €0,00
                </div>
              </div>
            </label>

            <label className="flex items-center p-2 rounded-lg hover:bg-white hover:bg-opacity-50 transition-colors cursor-pointer">
              <input
                type="radio"
                name="videodoorbell"
                checked={videodoorbellPaid}
                onChange={() => {
                  setVideodoorbellFree(false);
                  setVideodoorbellPaid(true);
                }}
                className="mr-3 text-blue-600"
              />
              <div className="flex-1">
                <span className="text-gray-700">Video Deurbel</span>
                <div className="text-sm text-gray-500">€249,99 incl. BTW</div>
              </div>
            </label>
          </div>
        </div>

        {/* Payment terms */}
        <div className="bg-white rounded-lg border p-4">
          <h3 className="font-semibold text-gray-900 mb-4">Betalingsvoorwaarden</h3>

          <div className="space-y-3">
            {['1_term', '2_terms', '3_terms'].map((terms) => (
              <label key={terms} className="flex items-center">
                <input
                  type="radio"
                  name="paymentTerms"
                  value={terms}
                  checked={paymentTerms === terms}
                  onChange={(e) => handlePaymentTermsChange(e.target.value)}
                  className="mr-2"
                />
                <span>{getPaymentTermsLabel(terms)}</span>
              </label>
            ))}
          </div>
        </div>

        {/* Discount slider */}
        <div className="bg-white rounded-xl shadow-sm border p-6">
          <h3 className="font-semibold text-gray-900 mb-6 text-lg">Korting op installatiekosten</h3>

          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-gray-700">Korting niveau:</span>
              <span className="font-semibold text-lg text-blue-600">
                {discountPercentage === 0 ? 'Geen korting' :
                 discountPercentage <= getMaxDiscountForTerms(paymentTerms) * 0.3 ? 'Lage korting' :
                 discountPercentage <= getMaxDiscountForTerms(paymentTerms) * 0.7 ? 'Gemiddelde korting' :
                 'Hoge korting'}
              </span>
            </div>

            <div className="relative">
              <input
                type="range"
                min="0"
                max={getMaxDiscountForTerms(paymentTerms)}
                step="0.5"
                value={discountPercentage}
                onChange={(e) => handleDiscountChange(parseFloat(e.target.value))}
                className="w-full h-3 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                style={{
                  background: `linear-gradient(to right, #3b82f6 0%, #3b82f6 ${(discountPercentage / getMaxDiscountForTerms(paymentTerms)) * 100}%, #e5e7eb ${(discountPercentage / getMaxDiscountForTerms(paymentTerms)) * 100}%, #e5e7eb 100%)`
                }}
              />
              <div className="flex justify-between text-xs text-gray-500 mt-2">
                <span>Geen korting</span>
                <span>Lichte korting</span>
                <span>Gemiddelde korting</span>
                <span>Maximale korting</span>
              </div>
            </div>

            <div className="bg-blue-50 rounded-lg p-3">
              <div className="text-sm text-blue-800">
                <strong>Betalingsvoorwaarden:</strong> {getPaymentTermsLabel(paymentTerms)}
                <br />
                <span className="text-xs">
                  {paymentTerms === '1_term' && 'Hoogste korting mogelijk'}
                  {paymentTerms === '2_terms' && 'Gemiddelde korting mogelijk'}
                  {paymentTerms === '3_terms' && 'Beperkte korting mogelijk'}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Dynamic Price Display - Always visible */}
        <div className="bg-gradient-to-r from-green-50 to-emerald-50 border-2 border-green-200 rounded-xl p-6 sticky top-4 z-10">
          <h3 className="font-bold text-green-900 mb-4 flex items-center text-xl">
            <FaCalculator className="mr-3 text-green-600" />
            Uw Offerte Prijzen
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Installation costs */}
            <div className="bg-white rounded-lg p-4 shadow-sm">
              <h4 className="font-semibold text-gray-800 mb-3">Eenmalige kosten</h4>
              <div className="space-y-2 text-sm">
                {calculations ? (
                  <>
                    <div className="flex justify-between">
                      <span>Basis installatiekosten:</span>
                      <span>€{calculations.installation_cost_before_discount.toFixed(2)}</span>
                    </div>
                    {calculations.discount_amount > 0 && (
                      <div className="flex justify-between text-green-600">
                        <span>Korting:</span>
                        <span>-€{calculations.discount_amount.toFixed(2)}</span>
                      </div>
                    )}
                    <div className="flex justify-between font-bold text-lg border-t pt-2 text-green-700">
                      <span>Totaal eenmalig:</span>
                      <span>€{calculations.final_installation_cost.toFixed(2)}</span>
                    </div>
                  </>
                ) : (
                  <div className="text-center text-gray-500">
                    <div className="animate-pulse">Berekenen...</div>
                  </div>
                )}
              </div>
            </div>

            {/* Monthly costs */}
            <div className="bg-white rounded-lg p-4 shadow-sm">
              <h4 className="font-semibold text-gray-800 mb-3">Maandelijkse kosten</h4>
              <div className="space-y-2 text-sm">
                {calculations ? (
                  <>
                    <div className="flex justify-between">
                      <span>Apparatuur:</span>
                      <span>€{calculations.monthly_equipment_cost.toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Meldkamer:</span>
                      <span>€{calculations.monthly_monitoring_cost.toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Onderhoud:</span>
                      <span>€{calculations.monthly_maintenance_cost.toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between font-bold text-lg border-t pt-2 text-green-700">
                      <span>Totaal per maand:</span>
                      <span>€{calculations.total_monthly_cost.toFixed(2)}</span>
                    </div>
                  </>
                ) : (
                  <div className="text-center text-gray-500">
                    <div className="animate-pulse">Berekenen...</div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Summary */}
          {calculations && (
            <div className="mt-4 pt-4 border-t border-green-200">
              <div className="text-center">
                <p className="text-sm text-green-800 mb-2">
                  <strong>{getCategoryTitle()}</strong> - {getPaymentTermsLabel(paymentTerms)}
                </p>
                <div className="flex justify-center items-center space-x-6">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-700">
                      €{calculations.final_installation_cost.toFixed(2)}
                    </div>
                    <div className="text-xs text-green-600">Eenmalig</div>
                  </div>
                  <div className="text-gray-400">+</div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-700">
                      €{calculations.total_monthly_cost.toFixed(2)}
                    </div>
                    <div className="text-xs text-green-600">Per maand</div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Continue button */}
        <div className="flex justify-between items-center bg-white rounded-xl shadow-sm border p-4">
          <button
            onClick={() => navigate('/simple-quotes')}
            className="text-gray-600 hover:text-gray-800 transition-colors font-medium flex items-center"
          >
            <FaArrowLeft className="mr-2" />
            Terug naar systeem keuze
          </button>

          <button
            onClick={handleContinue}
            className="bg-blue-600 text-white px-8 py-3 rounded-xl hover:bg-blue-700 transition-colors font-medium flex items-center shadow-lg disabled:bg-gray-400 disabled:cursor-not-allowed"
            disabled={loading}
          >
            {loading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Bezig met berekenen...
              </>
            ) : (
              <>
                Verder naar ondertekening
                <FaArrowLeft className="ml-2 rotate-180" />
              </>
            )}
          </button>
        </div>
      </div>
    </MobileContainer>
  );
};

export default SimpleQuoteBuilderPage;
