# Werkbon Template Instructies

## Overzicht
Dit document beschrijft hoe je een werkbon template maakt voor het AMSPM systeem. De template wordt gebruikt om werkbriefjes te genereren met klantgegevens, werkzaamheden, tijdregistratie en handtekeningen.

## Template Placeholders

### Klant Gegevens
- `{weeknummer}` - Weeknummer
- `{opdrachtgever}` - <PERSON><PERSON> (auto-populate van customer.name)
- `{werklocatie}` - Werklocatie (auto-populate van customer.address)
- `{postcode_plaats}` - Postcode en plaats (auto-populate van customer.postal_code + customer.city)
- `{afdeling}` - Afdeling
- `{naam}` - <PERSON><PERSON> van <PERSON> (auto-populate van customer.contact_person of customer.name)
- `{adres}` - Vol<PERSON><PERSON> adres (auto-populate van customer.address + postal_code + city)
- `{geb_datum}` - Geboortedatum
- `{personeelsnr}` - Personeelsnummer

### Werkzaamheden
- `{soort_werkzaamheden}` - Be<PERSON>rij<PERSON> van uitgevoerde werkzaamheden
- `{gebruikte_componenten}` - Lijst van gebruikte materialen en componenten
- `{bijzonderheden}` - Bijzonderheden of opmerkingen

### Tijdregistratie
- `{datum_1}` - Datum eerste dag
- `{van_tot_1_start}` - Starttijd eerste dag
- `{van_tot_1_end}` - Eindtijd eerste dag
- `{gewone_uren_1}` - Gewone uren eerste dag
- `{toeslag_1_perc}` - Percentage eerste toeslag
- `{toeslag_1_uren}` - Uren eerste toeslag
- `{toeslag_2_perc}` - Percentage tweede toeslag
- `{toeslag_2_uren}` - Uren tweede toeslag
- `{toeslag_3_perc}` - Percentage derde toeslag
- `{toeslag_3_uren}` - Uren derde toeslag
- `{totaal_uren_1}` - Totaal uren eerste dag

- `{datum_2}` - Datum tweede dag
- `{van_tot_2_start}` - Starttijd tweede dag
- `{van_tot_2_end}` - Eindtijd tweede dag
- `{gewone_uren_2}` - Gewone uren tweede dag
- `{totaal_uren_2}` - Totaal uren tweede dag

- `{totaal_gewone_uren}` - Totaal gewone uren
- `{totaal_toeslag_uren}` - Totaal toeslag uren
- `{totaal_alle_uren}` - Totaal alle uren

### Handtekeningen
- `{opdrachtgever_naam}` - Naam opdrachtgever (auto-populate van customer.contact_person of customer.name)
- `{opdrachtgever_firma}` - Firmanaam (auto-populate van customer.name)
- `{datum_handtekening}` - Datum handtekening (auto-populate met huidige datum)
- `{opdrachtgever_handtekening}` - Handtekening opdrachtgever (signature pad)
- `{uitzendkracht_handtekening}` - Handtekening uitzendkracht (signature pad)

## Stappen om Word Template te maken

### 1. Open Microsoft Word
Start een nieuw document in Microsoft Word.

### 2. Maak de Header
- Typ "W E R K B R I E F J E" gecentreerd bovenaan
- Gebruik een grote lettergrootte (24pt) en vetgedrukt
- Voeg wat ruimte toe onder de header

### 3. Klant Gegevens Sectie
Maak een sectie met de volgende velden in twee kolommen:

**Linker kolom:**
- Weeknummer: {weeknummer}
- Opdrachtgever: {opdrachtgever}
- Werklocatie: {werklocatie}
- Postcode/plaats: {postcode_plaats}
- Afdeling: {afdeling}

**Rechter kolom:**
- Naam: {naam}
- Adres: {adres}
- Postcode/plaats: {postcode_plaats}
- Geb.datum: {geb_datum}
- Personeelsnr.: {personeelsnr}

### 4. Werkzaamheden Sectie
Voeg de volgende velden toe:
- Soort werkzaamheden: {soort_werkzaamheden}
- Gebruikte componenten: {gebruikte_componenten}
- Bijzonderheden: {bijzonderheden}

### 5. Tijdregistratie Tabel
Maak een tabel met de volgende kolommen:
- Datum
- van-tot (2 kolommen)
- gewone uren
- toeslag ...% (3 kolommen)
- Totaal uren

Voeg minimaal 6 rijen toe (inclusief totaal rij).

### 6. Handtekeningen Sectie
Maak twee kolommen:

**Linker kolom:**
- "Handtekening, naam en firmanaam:"
- Ruimte voor handtekening: {opdrachtgever_handtekening}
- Naam: {opdrachtgever_naam}
- Firma: {opdrachtgever_firma}
- Datum: {datum_handtekening}

**Rechter kolom:**
- "Handtekening uitzendkracht:"
- Ruimte voor handtekening: {uitzendkracht_handtekening}

### 7. Verklaring
Voeg de tekst toe: "Wij verklaren ons akkoord met het hierboven vermelde totaal aantal uren."

## Template Detectie
Het systeem detecteert automatisch werkbon templates op basis van de volgende termen in het document:
- "Weeknummer"
- "Opdrachtgever"
- "gewone uren"
- "Handtekening uitzendkracht"
- "werkzaamheden"
- "gebruikte_componenten"
- "Personeelsnr"

## Opslaan
Sla het document op als `werkbon_template.docx` en upload het via de document template manager in het AMSPM systeem met document type "werkbon".

## Gebruik
Na het uploaden kunnen gebruikers:
1. Een werkbon template selecteren
2. Klantgegevens worden automatisch ingevuld
3. Werkzaamheden en tijdregistratie handmatig invullen
4. Handtekeningen digitaal plaatsen
5. Het document genereren als DOCX bestand
